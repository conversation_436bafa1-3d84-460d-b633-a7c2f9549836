#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PROFESSIONNEL BACCARAT LUPASCO
========================================

OBJECTIF PRINCIPAL :
- Analyser toutes les séquences (fenêtres glissantes) de longueur 3 d'INDEX5
- Construire une base de données des motifs
- Calculer les probabilités conditionnelles P(V₃|V₁,V₂)
- Prédire la 3ème valeur la plus probable pour tout doublet (v₁,v₂)
- Valider la précision des prédictions

ARCHITECTURE BASÉE SUR :
- REFERENCE_COMPLETE_BASE_SYNTHESES.md (porte d'accès aux connaissances)
- TECHNIQUE_CHARGEMENT_TRAITEMENT_JSON_MASSIF.txt (optimisations JSON)
- BASE/ (base de connaissances théoriques)

Auteur: Analyseur Scientifique Révolutionnaire
Version: 1.0
Date: 2025-07-02
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import re
import gc
from pathlib import Path
import logging
from datetime import datetime

# OPTIMISATIONS JSON HAUTE PERFORMANCE
try:
    import ijson
    HAS_IJSON = True
    print("🚀 ijson disponible - Streaming JSON activé")
except ImportError:
    HAS_IJSON = False
    print("⚠️  ijson non disponible - Chargement standard")

try:
    import orjson
    HAS_ORJSON = True
    print("🚀 orjson disponible - Parsing JSON ultra-rapide")
except ImportError:
    HAS_ORJSON = False
    print("⚠️  orjson non disponible - JSON standard")

try:
    import msgspec
    from msgspec import Struct
    MSGSPEC_AVAILABLE = True
    print("🚀 msgspec disponible - Performances révolutionnaires activées")
except ImportError:
    MSGSPEC_AVAILABLE = False
    print("⚠️  msgspec non disponible - Utilisation du JSON standard")

# OPTIMISATIONS NUMÉRIQUES
try:
    from numba import jit, prange
    HAS_NUMBA = True
    print("🚀 numba disponible - Calculs JIT ultra-rapides")
except ImportError:
    HAS_NUMBA = False
    print("⚠️  numba non disponible - Calculs Python standard")

# LIBRAIRIES SCIENTIFIQUES
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split

# CONFIGURATION LOGGING
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyseur_baccarat.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AnalyseurBaccaratLupasco:
    """
    Analyseur professionnel pour le système Baccarat Lupasco
    
    Implémente les 3 approches théoriques :
    1. Entropique (Shannon Information Theory)
    2. Fractale (Carnegie Mellon F4 System)
    3. Unifiée (Markov + Lupasco Rules + Fractal Complexity)
    """
    
    def __init__(self, dataset_path: str, chunk_size: int = 5000):
        """
        Initialise l'analyseur avec optimisations pour gros fichiers JSON
        
        Args:
            dataset_path: Chemin vers le fichier JSON de données
            chunk_size: Taille des chunks pour traitement optimisé
        """
        self.dataset_path = Path(dataset_path)
        self.chunk_size = chunk_size
        
        # DONNÉES PRINCIPALES
        self.data = None
        self.sequences_index5 = []
        self.sequences_metadata = {}
        
        # BASE DE DONNÉES DES MOTIFS
        self.patterns_db = defaultdict(Counter)  # {(v1,v2): Counter({v3: count})}
        self.conditional_probabilities = {}     # {(v1,v2): {v3: probability}}
        
        # MÉTRIQUES DE VALIDATION
        self.validation_results = {}
        self.prediction_accuracy = {}
        
        # OPTIMISATIONS MÉMOIRE
        self.memory_limit_mb = 20480  # 20GB pour système 28GB RAM
        self.use_streaming = True
        
        # VALEURS INDEX5 POSSIBLES (18 combinaisons)
        self.index5_values = [
            f"{i1}_{i2}_{i3}" 
            for i1 in [0, 1] 
            for i2 in ['A', 'B', 'C'] 
            for i3 in ['BANKER', 'PLAYER', 'TIE']
        ]
        
        logger.info(f"🔬 Analyseur Baccarat Lupasco initialisé")
        logger.info(f"📊 Dataset: {self.dataset_path}")
        logger.info(f"📦 Chunk size: {self.chunk_size}")
        logger.info(f"🎯 Valeurs INDEX5 possibles: {len(self.index5_values)}")
    
    def charger_dataset(self) -> bool:
        """
        Charge le dataset avec optimisations selon la taille du fichier
        
        Returns:
            bool: True si chargement réussi
        """
        logger.info("🔄 Début du chargement du dataset...")
        
        if not self.dataset_path.exists():
            logger.error(f"❌ Fichier non trouvé: {self.dataset_path}")
            return False
        
        # Détection de la taille du fichier
        file_size_mb = self.dataset_path.stat().st_size / (1024 * 1024)
        logger.info(f"📊 Taille fichier détectée: {file_size_mb:.1f} MB")
        
        # Choix de la méthode optimale
        if file_size_mb > 5000:  # > 5GB : streaming obligatoire
            logger.info("🌊 Fichier volumineux - Mode streaming forcé")
            return self._charger_streaming()
        elif file_size_mb > 500:  # 500MB-5GB : streaming recommandé
            logger.info("🚀 Mode streaming recommandé")
            return self._charger_streaming() if HAS_IJSON else self._charger_standard()
        else:  # < 500MB : chargement direct optimisé
            logger.info("📂 Chargement direct optimisé")
            return self._charger_standard()
    
    def _charger_streaming(self) -> bool:
        """Chargement par streaming pour gros fichiers"""
        try:
            logger.info("🌊 Chargement streaming avec ijson...")
            
            with open(self.dataset_path, 'rb') as f:
                # Stream des parties
                parties_stream = ijson.items(f, 'parties.item')
                
                chunk_data = []
                parties_processed = 0
                
                for partie in parties_stream:
                    chunk_data.append(partie)
                    parties_processed += 1
                    
                    # Traiter chunk quand plein
                    if len(chunk_data) >= self.chunk_size:
                        self._traiter_chunk(chunk_data)
                        chunk_data = []
                        gc.collect()
                        
                        logger.info(f"📦 Chunk traité: {parties_processed} parties")
                
                # Traiter le dernier chunk
                if chunk_data:
                    self._traiter_chunk(chunk_data)
            
            logger.info(f"✅ Streaming terminé: {parties_processed} parties traitées")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur streaming: {e}")
            return self._charger_standard()  # Fallback
    
    def _charger_standard(self) -> bool:
        """Chargement standard optimisé"""
        try:
            logger.info("📂 Chargement standard...")
            
            # Utiliser orjson si disponible
            if HAS_ORJSON:
                with open(self.dataset_path, 'rb') as f:
                    self.data = orjson.loads(f.read())
                logger.info("🚀 Parsing orjson ultra-rapide utilisé")
            else:
                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info("📂 Parsing JSON standard utilisé")
            
            # Traitement par chunks
            parties = self.data.get('parties', [])
            for i in range(0, len(parties), self.chunk_size):
                chunk = parties[i:i + self.chunk_size]
                self._traiter_chunk(chunk)
                
                if i % (self.chunk_size * 10) == 0:  # GC tous les 10 chunks
                    gc.collect()
            
            logger.info(f"✅ Chargement terminé: {len(parties)} parties")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur chargement: {e}")
            return False
    
    def _traiter_chunk(self, chunk_parties: List[Dict]) -> None:
        """
        Traite un chunk de parties pour extraire les séquences INDEX5
        
        Args:
            chunk_parties: Liste des parties à traiter
        """
        for partie in chunk_parties:
            if 'mains' not in partie:
                continue
            
            partie_number = partie.get('partie_number', 0)
            
            for main in partie['mains']:
                # Filtrer les mains invalides
                if main.get('main_number') is None:
                    continue
                
                index5 = main.get('index5', '').strip()
                if not index5 or not self._valider_index5(index5):
                    continue
                
                # Ajouter à la séquence
                self.sequences_index5.append(index5)
                
                # Métadonnées
                if 'metadata' not in self.sequences_metadata:
                    self.sequences_metadata['metadata'] = []
                
                self.sequences_metadata['metadata'].append({
                    'partie_number': partie_number,
                    'main_number': main.get('main_number'),
                    'manche_pb_number': main.get('manche_pb_number'),
                    'timestamp': main.get('timestamp', ''),
                    'index1': main.get('index1'),
                    'index2': main.get('index2', ''),
                    'index3': main.get('index3', ''),
                    'cards_count': main.get('cards_count', 0)
                })
    
    def _valider_index5(self, index5: str) -> bool:
        """
        Valide le format INDEX5
        
        Args:
            index5: Valeur INDEX5 à valider
            
        Returns:
            bool: True si valide
        """
        pattern = r'^[01]_[ABC]_(BANKER|PLAYER|TIE)$'
        return bool(re.match(pattern, index5))
    
    def extraire_sequences_ordre3(self) -> int:
        """
        Extrait toutes les séquences de longueur 3 (fenêtres glissantes)
        
        Returns:
            int: Nombre de séquences extraites
        """
        logger.info("🔍 Extraction des séquences d'ordre 3...")
        
        if len(self.sequences_index5) < 3:
            logger.warning("⚠️  Pas assez de données pour les séquences d'ordre 3")
            return 0
        
        sequences_count = 0
        
        # Fenêtres glissantes de longueur 3
        for i in range(len(self.sequences_index5) - 2):
            v1 = self.sequences_index5[i]
            v2 = self.sequences_index5[i + 1]
            v3 = self.sequences_index5[i + 2]
            
            # Ajouter à la base de données des motifs
            pattern_key = (v1, v2)
            self.patterns_db[pattern_key][v3] += 1
            sequences_count += 1
        
        logger.info(f"✅ {sequences_count} séquences d'ordre 3 extraites")
        logger.info(f"📊 {len(self.patterns_db)} motifs uniques identifiés")
        
        return sequences_count
    
    def calculer_probabilites_conditionnelles(self) -> None:
        """
        Calcule P(V₃|V₁,V₂) pour tous les motifs
        """
        logger.info("📊 Calcul des probabilités conditionnelles P(V₃|V₁,V₂)...")
        
        for pattern_key, v3_counts in self.patterns_db.items():
            total_occurrences = sum(v3_counts.values())
            
            if total_occurrences == 0:
                continue
            
            # Calculer P(V₃|V₁,V₂) pour chaque V₃
            probabilities = {}
            for v3, count in v3_counts.items():
                probabilities[v3] = count / total_occurrences
            
            self.conditional_probabilities[pattern_key] = probabilities
        
        logger.info(f"✅ Probabilités calculées pour {len(self.conditional_probabilities)} motifs")

    def predire_v3_optimal(self, v1: str, v2: str) -> Tuple[str, float]:
        """
        Prédit la 3ème valeur la plus probable pour un doublet (v1,v2)

        Args:
            v1: Première valeur INDEX5
            v2: Deuxième valeur INDEX5

        Returns:
            Tuple[str, float]: (valeur_predite, probabilite)
        """
        pattern_key = (v1, v2)

        if pattern_key not in self.conditional_probabilities:
            # Fallback : prédiction basée sur la fréquence globale
            return self._predire_fallback()

        probabilities = self.conditional_probabilities[pattern_key]

        # Trouver la valeur avec la probabilité maximale
        best_v3 = max(probabilities.items(), key=lambda x: x[1])

        return best_v3[0], best_v3[1]

    def _predire_fallback(self) -> Tuple[str, float]:
        """Prédiction de fallback basée sur la fréquence globale"""
        # Compter toutes les occurrences de V3
        global_counts = Counter()
        for v3_counts in self.patterns_db.values():
            for v3, count in v3_counts.items():
                global_counts[v3] += count

        if not global_counts:
            return "0_A_BANKER", 0.0  # Valeur par défaut

        total = sum(global_counts.values())
        most_common = global_counts.most_common(1)[0]

        return most_common[0], most_common[1] / total

    def valider_predictions(self, test_ratio: float = 0.2) -> Dict[str, float]:
        """
        Valide la précision des prédictions par validation croisée

        Args:
            test_ratio: Ratio des données pour le test

        Returns:
            Dict[str, float]: Métriques de validation
        """
        logger.info("🎯 Validation des prédictions...")

        if len(self.sequences_index5) < 10:
            logger.warning("⚠️  Pas assez de données pour la validation")
            return {}

        # Préparer les données pour validation
        X, y = [], []

        for i in range(len(self.sequences_index5) - 2):
            v1 = self.sequences_index5[i]
            v2 = self.sequences_index5[i + 1]
            v3_actual = self.sequences_index5[i + 2]

            X.append((v1, v2))
            y.append(v3_actual)

        # Division train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_ratio, random_state=42, stratify=None
        )

        # Construire le modèle sur les données d'entraînement
        train_patterns = defaultdict(Counter)
        for (v1, v2), v3 in zip(X_train, y_train):
            train_patterns[(v1, v2)][v3] += 1

        # Calculer les probabilités d'entraînement
        train_probabilities = {}
        for pattern_key, v3_counts in train_patterns.items():
            total = sum(v3_counts.values())
            probabilities = {v3: count/total for v3, count in v3_counts.items()}
            train_probabilities[pattern_key] = probabilities

        # Faire les prédictions sur le test
        y_pred = []
        prediction_confidences = []

        for v1, v2 in X_test:
            pattern_key = (v1, v2)

            if pattern_key in train_probabilities:
                probabilities = train_probabilities[pattern_key]
                best_v3 = max(probabilities.items(), key=lambda x: x[1])
                y_pred.append(best_v3[0])
                prediction_confidences.append(best_v3[1])
            else:
                # Fallback
                fallback_pred, fallback_conf = self._predire_fallback()
                y_pred.append(fallback_pred)
                prediction_confidences.append(fallback_conf)

        # Calculer les métriques
        accuracy = accuracy_score(y_test, y_pred)

        # Métriques détaillées
        unique_labels = list(set(y_test + y_pred))
        precision = precision_score(y_test, y_pred, labels=unique_labels, average='weighted', zero_division=0)
        recall = recall_score(y_test, y_pred, labels=unique_labels, average='weighted', zero_division=0)
        f1 = f1_score(y_test, y_pred, labels=unique_labels, average='weighted', zero_division=0)

        # Confiance moyenne
        avg_confidence = np.mean(prediction_confidences) if prediction_confidences else 0.0

        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'average_confidence': avg_confidence,
            'test_samples': len(y_test),
            'train_samples': len(y_train)
        }

        self.validation_results = metrics

        logger.info(f"✅ Validation terminée:")
        logger.info(f"   📊 Précision: {accuracy:.4f}")
        logger.info(f"   📊 F1-Score: {f1:.4f}")
        logger.info(f"   📊 Confiance moyenne: {avg_confidence:.4f}")
        logger.info(f"   📊 Échantillons test: {len(y_test)}")

        return metrics

    def generer_rapport_complet(self) -> str:
        """
        Génère un rapport complet d'analyse

        Returns:
            str: Rapport formaté
        """
        rapport = []
        rapport.append("=" * 80)
        rapport.append("RAPPORT D'ANALYSE BACCARAT LUPASCO")
        rapport.append("=" * 80)
        rapport.append(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        rapport.append(f"Dataset: {self.dataset_path}")
        rapport.append("")

        # DONNÉES
        rapport.append("📊 DONNÉES ANALYSÉES")
        rapport.append("-" * 40)
        rapport.append(f"Séquences INDEX5 totales: {len(self.sequences_index5)}")
        rapport.append(f"Motifs uniques identifiés: {len(self.patterns_db)}")
        rapport.append(f"Probabilités conditionnelles: {len(self.conditional_probabilities)}")
        rapport.append("")

        # TOP MOTIFS
        rapport.append("🎯 TOP 10 MOTIFS LES PLUS FRÉQUENTS")
        rapport.append("-" * 40)

        # Calculer fréquences totales par motif
        motif_frequencies = {}
        for pattern_key, v3_counts in self.patterns_db.items():
            total_count = sum(v3_counts.values())
            motif_frequencies[pattern_key] = total_count

        top_motifs = sorted(motif_frequencies.items(), key=lambda x: x[1], reverse=True)[:10]

        for i, (pattern_key, count) in enumerate(top_motifs, 1):
            v1, v2 = pattern_key
            rapport.append(f"{i:2d}. ({v1}, {v2}) → {count} occurrences")

            # Afficher les probabilités pour ce motif
            if pattern_key in self.conditional_probabilities:
                probs = self.conditional_probabilities[pattern_key]
                sorted_probs = sorted(probs.items(), key=lambda x: x[1], reverse=True)
                for v3, prob in sorted_probs[:3]:  # Top 3 prédictions
                    rapport.append(f"    → {v3}: {prob:.3f}")

        rapport.append("")

        # VALIDATION
        if self.validation_results:
            rapport.append("🎯 RÉSULTATS DE VALIDATION")
            rapport.append("-" * 40)
            for metric, value in self.validation_results.items():
                if isinstance(value, float):
                    rapport.append(f"{metric.replace('_', ' ').title()}: {value:.4f}")
                else:
                    rapport.append(f"{metric.replace('_', ' ').title()}: {value}")

        rapport.append("")
        rapport.append("=" * 80)

        return "\n".join(rapport)

    def sauvegarder_resultats(self, output_dir: str = "resultats") -> None:
        """
        Sauvegarde tous les résultats d'analyse

        Args:
            output_dir: Répertoire de sortie
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 1. Rapport complet
        rapport_path = output_path / f"rapport_analyse_{timestamp}.txt"
        with open(rapport_path, 'w', encoding='utf-8') as f:
            f.write(self.generer_rapport_complet())

        # 2. Base de données des motifs (JSON)
        patterns_path = output_path / f"patterns_db_{timestamp}.json"
        patterns_serializable = {
            f"{k[0]}|{k[1]}": dict(v) for k, v in self.patterns_db.items()
        }
        with open(patterns_path, 'w', encoding='utf-8') as f:
            json.dump(patterns_serializable, f, indent=2, ensure_ascii=False)

        # 3. Probabilités conditionnelles (JSON)
        probs_path = output_path / f"conditional_probabilities_{timestamp}.json"
        probs_serializable = {
            f"{k[0]}|{k[1]}": v for k, v in self.conditional_probabilities.items()
        }
        with open(probs_path, 'w', encoding='utf-8') as f:
            json.dump(probs_serializable, f, indent=2, ensure_ascii=False)

        # 4. Séquences INDEX5 (CSV)
        sequences_path = output_path / f"sequences_index5_{timestamp}.csv"
        df_sequences = pd.DataFrame({
            'position': range(len(self.sequences_index5)),
            'index5': self.sequences_index5
        })
        df_sequences.to_csv(sequences_path, index=False, encoding='utf-8')

        # 5. Métriques de validation (JSON)
        if self.validation_results:
            metrics_path = output_path / f"validation_metrics_{timestamp}.json"
            with open(metrics_path, 'w', encoding='utf-8') as f:
                json.dump(self.validation_results, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ Résultats sauvegardés dans: {output_path}")
        logger.info(f"   📄 Rapport: {rapport_path}")
        logger.info(f"   📊 Motifs: {patterns_path}")
        logger.info(f"   📊 Probabilités: {probs_path}")
        logger.info(f"   📊 Séquences: {sequences_path}")
        if self.validation_results:
            logger.info(f"   📊 Métriques: {metrics_path}")


def main():
    """
    Fonction principale d'analyse
    """
    print("🔬 ANALYSEUR PROFESSIONNEL BACCARAT LUPASCO")
    print("=" * 60)

    # Configuration
    dataset_path = "exemple.json"  # Chemin vers le dataset

    # Initialisation
    analyseur = AnalyseurBaccaratLupasco(dataset_path)

    try:
        # ÉTAPE 1: Chargement des données
        print("\n🔄 ÉTAPE 1: Chargement du dataset...")
        if not analyseur.charger_dataset():
            print("❌ Échec du chargement du dataset")
            return

        # ÉTAPE 2: Extraction des séquences
        print("\n🔍 ÉTAPE 2: Extraction des séquences d'ordre 3...")
        sequences_count = analyseur.extraire_sequences_ordre3()
        if sequences_count == 0:
            print("❌ Aucune séquence extraite")
            return

        # ÉTAPE 3: Calcul des probabilités
        print("\n📊 ÉTAPE 3: Calcul des probabilités conditionnelles...")
        analyseur.calculer_probabilites_conditionnelles()

        # ÉTAPE 4: Validation
        print("\n🎯 ÉTAPE 4: Validation des prédictions...")
        metrics = analyseur.valider_predictions()

        # ÉTAPE 5: Génération du rapport
        print("\n📄 ÉTAPE 5: Génération du rapport...")
        rapport = analyseur.generer_rapport_complet()
        print(rapport)

        # ÉTAPE 6: Sauvegarde
        print("\n💾 ÉTAPE 6: Sauvegarde des résultats...")
        analyseur.sauvegarder_resultats()

        print("\n✅ ANALYSE TERMINÉE AVEC SUCCÈS!")

        # Exemple de prédiction
        if len(analyseur.sequences_index5) >= 2:
            v1, v2 = analyseur.sequences_index5[0], analyseur.sequences_index5[1]
            prediction, confidence = analyseur.predire_v3_optimal(v1, v2)
            print(f"\n🎯 EXEMPLE DE PRÉDICTION:")
            print(f"   Doublet: ({v1}, {v2})")
            print(f"   Prédiction: {prediction}")
            print(f"   Confiance: {confidence:.4f}")

    except Exception as e:
        logger.error(f"❌ Erreur lors de l'analyse: {e}")
        raise


if __name__ == "__main__":
    main()
