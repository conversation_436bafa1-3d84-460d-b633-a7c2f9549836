TECHNIQUE DE CHARGEMENT ET TRAITEMENT OPTIMAL D'UN FICHIER JSON MASSIF
=======================================================================
Basé sur l'analyse de : analyseur_scientifique_revolutionnaire.py

OBJECTIF : Charger et traiter efficacement des fichiers JSON de plusieurs GB
CONTEXTE : Système optimisé pour 28GB RAM, fichiers jusqu'à 5GB+

================================================================================
I. ARCHITECTURE MULTI-NIVEAUX DE CHARGEMENT
================================================================================

1. DÉTECTION AUTOMATIQUE DE LA MÉTHODE OPTIMALE
-----------------------------------------------
```python
def charger_dataset(self) -> bool:
    file_size_mb = os.path.getsize(self.dataset_path) / (1024 * 1024)
    
    if file_size_mb > 5000:  # Fichiers > 5GB : streaming obligatoire
        if HAS_IJSON:
            return self._charger_dataset_streaming()
        else:
            return self._charger_dataset_standard()
    elif self.use_streaming and HAS_IJSON:
        return self._charger_dataset_streaming()
    elif MSGSPEC_AVAILABLE and file_size_mb < 500:  # msgspec pour < 500MB
        return self._charger_dataset_msgspec()
    else:
        return self._charger_dataset_standard()
```

2. HIÉRARCHIE DES MÉTHODES (PAR ORDRE DE PERFORMANCE)
----------------------------------------------------
- STREAMING HYBRIDE (ijson + msgspec) : Fichiers > 5GB
- MSGSPEC PUR : Fichiers < 500MB, performance maximale
- ORJSON STANDARD : Fallback ultra-rapide
- JSON STANDARD : Fallback de base

================================================================================
II. LIBRAIRIES D'OPTIMISATION CRITIQUES
================================================================================

1. LIBRAIRIES DE PARSING JSON HAUTE PERFORMANCE
----------------------------------------------
```python
# STREAMING JSON POUR DATASETS MASSIFS
try:
    import ijson
    HAS_IJSON = True
    print("🚀 ijson disponible - Streaming JSON activé")
except ImportError:
    HAS_IJSON = False

# JSON ULTRA-RAPIDE
try:
    import orjson
    HAS_ORJSON = True
    print("🚀 orjson disponible - Parsing JSON ultra-rapide")
except ImportError:
    HAS_ORJSON = False

# STRUCTURES OPTIMISÉES
try:
    import msgspec
    from msgspec import Struct
    MSGSPEC_AVAILABLE = True
    print("🚀 msgspec disponible - Performances révolutionnaires activées")
except ImportError:
    MSGSPEC_AVAILABLE = False
```

2. OPTIMISATIONS NUMÉRIQUES
---------------------------
```python
# OPTIMISATIONS NUMBA JIT
try:
    from numba import jit, prange
    HAS_NUMBA = True
    print("🚀 numba disponible - Calculs JIT ultra-rapides")
except ImportError:
    HAS_NUMBA = False

import gc  # Garbage collection explicite
```

================================================================================
III. MÉTHODE 1 : STREAMING HYBRIDE (FICHIERS > 5GB)
================================================================================

1. PRINCIPE : ijson STREAMING + msgspec PARSING
----------------------------------------------
```python
def _charger_dataset_streaming(self) -> bool:
    print(f"🌊 STREAMING HYBRIDE RÉVOLUTIONNAIRE : ijson + msgspec")
    print(f"📦 Traitement par chunks de {self.chunk_size} parties")
    
    parties_processed = 0
    chunk_data = []
    
    with open(self.dataset_path, 'rb') as f:
        # Utiliser ijson.items pour extraire directement les parties
        parties_stream = ijson.items(f, 'parties.item')
        
        for partie in parties_stream:
            chunk_data.append(partie)
            parties_processed += 1
            
            # Traiter chunk quand plein avec msgspec
            if len(chunk_data) >= self.chunk_size:
                self._traiter_chunk_streaming_msgspec(chunk_data)
                chunk_data = []
                gc.collect()  # Libération mémoire explicite
            
            # Arrêter si limite atteinte
            if parties_processed >= self.nb_parties_analyse:
                break
        
        # Traiter le dernier chunk
        if chunk_data:
            self._traiter_chunk_streaming_msgspec(chunk_data)
```

2. TRAITEMENT DES CHUNKS AVEC MSGSPEC
------------------------------------
```python
def _traiter_chunk_streaming_msgspec(self, chunk_data):
    # OPTIMISATION MSGSPEC : Parser le chunk entier si msgspec disponible
    if MSGSPEC_AVAILABLE:
        try:
            parsed_chunk = parse_chunk_msgspec(chunk_data)
            chunk_to_process = parsed_chunk
        except Exception as e:
            chunk_to_process = chunk_data  # Fallback
    else:
        chunk_to_process = chunk_data
    
    # Extraction optimisée des séquences vers arrays pré-alloués
    for partie_data in chunk_to_process:
        # Gestion des structures msgspec et dict
        if hasattr(partie_data, 'mains'):  # Structure msgspec
            mains = partie_data.mains
        elif 'mains' in partie_data:  # Structure dict
            mains = partie_data['mains']
        else:
            continue
        
        for main_data in mains:
            # FILTRAGE : Ignorer les mains null (main_number = null)
            main_number = None
            if hasattr(main_data, 'main_number'):
                main_number = getattr(main_data, 'main_number', None)
            else:
                main_number = main_data.get('main_number')
            
            if main_number is None:
                continue
            
            # Extraction vers arrays pré-alloués avec validation
            # [Code d'extraction optimisée...]
```

================================================================================
IV. MÉTHODE 2 : MSGSPEC PUR (FICHIERS < 500MB)
================================================================================

1. STRUCTURES MSGSPEC OPTIMISÉES
-------------------------------
```python
if MSGSPEC_AVAILABLE:
    class CarteStruct(Struct):
        rang: str
        couleur: str
        valeur: int
    
    class MainStruct(Struct):
        main_number: Optional[int]
        manche_pb_number: Optional[int]
        cartes_player: List[CarteStruct]
        cartes_banker: List[CarteStruct]
        total_cartes_distribuees: int
        score_player: int
        score_banker: int
        index1: Optional[int]
        cards_count: int
        index2: str
        index3: str
        index5: str
        timestamp: str
    
    class DatasetStruct(Struct):
        metadata: Dict[str, Any]
        configuration: Dict[str, Any]
        parties: List[PartieStruct]
```

2. CHARGEMENT MSGSPEC ULTRA-RAPIDE
---------------------------------
```python
def _charger_dataset_msgspec(self) -> bool:
    with open(self.dataset_path, 'rb') as f:
        data_raw = f.read()
        print(f"📊 Taille fichier : {len(data_raw) / (1024*1024):.1f} MB")
    
    # Parsing ultra-rapide avec validation de schéma
    try:
        self.data = msgspec.json.decode(data_raw, type=DatasetStruct)
        print("✅ Parsing msgspec réussi avec validation intégrée")
        
        # Conversion pour compatibilité avec le reste du code
        self.data = {
            'metadata': self.data.metadata,
            'configuration': self.data.configuration,
            'parties': [/* conversion structures */]
        }
    except Exception as e:
        return self._charger_dataset_standard()  # Fallback
```

================================================================================
V. MÉTHODE 3 : ORJSON STANDARD (FALLBACK RAPIDE)
================================================================================

```python
def _charger_dataset_standard(self) -> bool:
    # Utiliser orjson si disponible pour parsing ultra-rapide
    if HAS_ORJSON:
        with open(self.dataset_path, 'rb') as f:
            self.data = orjson.loads(f.read())
        print("🚀 Parsing orjson ultra-rapide utilisé")
    else:
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print("📂 Parsing JSON standard utilisé")
    
    # Traitement par chunks pour optimiser la mémoire
    if len(self.data['parties']) > self.nb_parties_analyse:
        parties_to_process = self.data['parties'][:self.nb_parties_analyse]
        
        # Libérer immédiatement les parties non utilisées
        self.data['parties'] = None
        gc.collect()
        
        # Traiter par chunks massifs avec 28GB RAM
        for i in range(0, len(parties_to_process), self.chunk_size):
            chunk = parties_to_process[i:i + self.chunk_size]
            self._traiter_chunk_standard(chunk)
            
            # Libération mémoire moins fréquente avec 28GB RAM
            if i % (self.chunk_size * 10) == 0:  # Tous les 10 chunks
                gc.collect()
```

================================================================================
VI. OPTIMISATIONS MÉMOIRE CRITIQUES
================================================================================

1. PRÉ-ALLOCATION MASSIVE POUR 28GB RAM
--------------------------------------
```python
# OPTIMISATIONS RÉVOLUTIONNAIRES POUR DATASETS MASSIFS - 28GB RAM DISPONIBLE
self.chunk_size = 5000  # Chunks massifs avec 28GB RAM
self.use_streaming = nb_parties_analyse > 10000  # Streaming agressif
self.memory_limit_mb = 20480  # Limite mémoire 20GB (réserve 8GB système)

# PRÉ-ALLOCATION MASSIVE POUR 28GB RAM - PERFORMANCE RÉVOLUTIONNAIRE
estimated_mains = nb_parties_analyse * 120  # Estimation généreuse
buffer_multiplier = 2.0  # Buffer 2x plus grand pour éviter réallocations
final_size = int(estimated_mains * buffer_multiplier)

self.sequences_preallocated = {
    'index1': np.zeros(final_size, dtype=np.int8),
    'index2': np.empty(final_size, dtype=object),  # Object pour chaînes
    'index3': np.empty(final_size, dtype=object),  # Object pour chaînes
    'index5': np.empty(final_size, dtype=object),  # Object pour chaînes
    'cards_count': np.zeros(final_size, dtype=np.int8)
}
self.sequences_counters = {key: 0 for key in self.sequences_preallocated.keys()}
```

2. GESTION EXPLICITE DE LA MÉMOIRE
---------------------------------
```python
import gc  # Garbage collection explicite

# Libération mémoire après chaque chunk
if len(chunk_data) >= self.chunk_size:
    self._traiter_chunk_streaming_msgspec(chunk_data)
    chunk_data = []
    gc.collect()  # Force garbage collection

# Libération mémoire moins fréquente avec 28GB RAM
if i % (self.chunk_size * 10) == 0:  # Tous les 10 chunks (50,000 parties)
    gc.collect()

# Libération des arrays temporaires après extraction
self.sequences_preallocated = None
self.sequences_counters = None
gc.collect()
```

================================================================================
VII. VALIDATION ET CONVERSION DE DONNÉES
================================================================================

1. VALIDATION STRICTE AVEC CONVERSION DE TYPE
--------------------------------------------
```python
def _traiter_chunk_standard(self, chunk_parties):
    for partie in chunk_parties:
        if 'mains' in partie:
            for main in partie['mains']:
                # FILTRAGE : Ignorer les mains null (main_number = null)
                if main.get('main_number') is None:
                    continue

                # Extraction optimisée vers arrays pré-alloués avec validation
                for index_name in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
                    if index_name in main and main[index_name] is not None:
                        counter = self.sequences_counters[index_name]
                        if counter < len(self.sequences_preallocated[index_name]):
                            value = main[index_name]

                            if index_name in ['index1', 'cards_count']:
                                # Entiers : validation et conversion STRICTE
                                try:
                                    if isinstance(value, str):
                                        converted_value = int(value.strip())
                                    else:
                                        converted_value = int(value)

                                    # Validation des valeurs INDEX1
                                    if index_name == 'index1' and converted_value not in [0, 1]:
                                        print(f"⚠️  INDEX1 valeur invalide: {converted_value}")
                                        continue

                                    self.sequences_preallocated[index_name][counter] = converted_value
                                    self.sequences_counters[index_name] += 1
                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Erreur conversion {index_name}: {value} -> {e}")
                            else:
                                # Chaînes : validation et conversion STRICTE
                                try:
                                    converted_value = str(value).strip()
                                    if converted_value:  # Ignorer les chaînes vides
                                        # Validation des valeurs INDEX2/3/5
                                        if index_name == 'index2' and converted_value not in ['A', 'B', 'C']:
                                            print(f"⚠️  INDEX2 valeur invalide: {converted_value}")
                                            continue
                                        elif index_name == 'index3' and converted_value not in ['BANKER', 'PLAYER', 'TIE']:
                                            print(f"⚠️  INDEX3 valeur invalide: {converted_value}")
                                            continue
                                        elif index_name == 'index5':
                                            # Validation format INDEX5
                                            expected_pattern = r'^[01]_[ABC]_(BANKER|PLAYER|TIE)$'
                                            if not re.match(expected_pattern, converted_value):
                                                print(f"⚠️  INDEX5 format invalide: {converted_value}")
                                                continue

                                        self.sequences_preallocated[index_name][counter] = converted_value
                                        self.sequences_counters[index_name] += 1
                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Erreur conversion {index_name}: {value} -> {e}")
```

2. VALIDATION CRITIQUE DES DONNÉES EXTRAITES
-------------------------------------------
```python
def _valider_donnees_extraites(self):
    print(f"\n🔍 VALIDATION CRITIQUE DES DONNÉES EXTRAITES :")

    for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
        if key in self.sequences:
            seq = self.sequences[key]
            print(f"   • {key}: {len(seq)} éléments")

            if len(seq) > 0:
                # Échantillon des premières valeurs
                sample = seq[:10] if len(seq) >= 10 else seq
                print(f"     → Échantillon : {sample}")

                # Valeurs uniques
                if key == 'index1':
                    unique_vals = np.unique(seq)
                    print(f"     → Valeurs uniques INDEX1 : {unique_vals}")
                    if len(unique_vals) == 2 and 0 in unique_vals and 1 in unique_vals:
                        print(f"     → ✅ INDEX1 valide (0/1)")
                    else:
                        print(f"     → ❌ INDEX1 invalide : {unique_vals}")

                elif key == 'index2':
                    unique_vals = np.unique(seq)
                    expected = {'A', 'B', 'C'}
                    if set(unique_vals).issubset(expected):
                        print(f"     → ✅ INDEX2 valide (A/B/C)")
                    else:
                        print(f"     → ❌ INDEX2 invalide : {unique_vals}")

                elif key == 'index3':
                    unique_vals = np.unique(seq)
                    expected = {'BANKER', 'PLAYER', 'TIE'}
                    if set(unique_vals).issubset(expected):
                        print(f"     → ✅ INDEX3 valide (BANKER/PLAYER/TIE)")
                    else:
                        print(f"     → ❌ INDEX3 invalide : {unique_vals}")
            else:
                print(f"     → ❌ SÉQUENCE VIDE !")
```

================================================================================
VIII. OPTIMISATIONS NUMBA JIT POUR CALCULS INTENSIFS
================================================================================

1. FONCTIONS JIT COMPILÉES POUR PERFORMANCE MAXIMALE
---------------------------------------------------
```python
if HAS_NUMBA:
    @jit(nopython=True, parallel=True, cache=True)
    def calcul_entropie_batch_jit(sequences_matrix):
        """Calcul d'entropies en batch ultra-rapide avec Numba JIT"""
        nb_sequences = sequences_matrix.shape[0]
        entropies = np.zeros(nb_sequences, dtype=np.float64)

        for i in prange(nb_sequences):
            sequence = sequences_matrix[i]
            # Calcul entropie Shannon optimisé
            counts = np.bincount(sequence.astype(np.int32))
            total = len(sequence)
            entropie = 0.0
            for count in counts:
                if count > 0:
                    p = count / total
                    entropie -= p * np.log2(p)
            entropies[i] = entropie

        return entropies

    @jit(nopython=True, parallel=True, cache=True)
    def conversion_sequences_batch_jit(sequences_list):
        """Conversion optimisée de listes en arrays numpy"""
        return np.array(sequences_list, dtype=np.int32)
```

2. FALLBACK SANS JIT
-------------------
```python
else:
    def calcul_entropie_batch_jit(sequences_matrix):
        """Version fallback sans JIT"""
        entropies = []
        for sequence in sequences_matrix:
            counts = np.bincount(sequence.astype(np.int32))
            total = len(sequence)
            entropie = 0.0
            for count in counts:
                if count > 0:
                    p = count / total
                    entropie -= p * np.log2(p)
            entropies.append(entropie)
        return np.array(entropies)
```

================================================================================
IX. CONFIGURATION SYSTÈME OPTIMISÉE
================================================================================

1. PARAMÈTRES POUR 28GB RAM
--------------------------
```python
# OPTIMISATIONS RÉVOLUTIONNAIRES POUR DATASETS MASSIFS - 28GB RAM DISPONIBLE
self.chunk_size = 5000  # Chunks massifs avec 28GB RAM
self.use_streaming = nb_parties_analyse > 10000  # Streaming agressif
self.memory_limit_mb = 20480  # Limite mémoire 20GB (réserve 8GB pour le système)

print(f"🚀 Mode streaming: {'Activé' if self.use_streaming else 'Désactivé'}")
print(f"📦 Taille chunks: {self.chunk_size:,} parties")
print(f"💾 Limite mémoire: {self.memory_limit_mb:,} MB ({self.memory_limit_mb/1024:.1f} GB)")
print(f"🎯 RAM DISPONIBLE : 28 GB - Configuration optimisée pour performance maximale")
```

2. SEUILS DE DÉCISION AUTOMATIQUE
--------------------------------
```python
if file_size_mb > 5000:  # Fichiers > 5GB : streaming obligatoire
    print("🌊 Fichier volumineux détecté - Mode streaming forcé")
    if HAS_IJSON:
        return self._charger_dataset_streaming()
    else:
        return self._charger_dataset_standard()
elif self.use_streaming and HAS_IJSON:
    return self._charger_dataset_streaming()
elif MSGSPEC_AVAILABLE and file_size_mb < 500:  # msgspec seulement pour fichiers < 500MB
    return self._charger_dataset_msgspec()
else:
    return self._charger_dataset_standard()
```

================================================================================
X. RÉSUMÉ DES TECHNIQUES CLÉS
================================================================================

1. HIÉRARCHIE DES PERFORMANCES
-----------------------------
1. STREAMING HYBRIDE (ijson + msgspec) : Fichiers > 5GB
2. MSGSPEC PUR : Fichiers < 500MB, validation de schéma intégrée
3. ORJSON : Parsing JSON 2-3x plus rapide que json standard
4. JSON STANDARD : Fallback de base

2. OPTIMISATIONS MÉMOIRE CRITIQUES
---------------------------------
- Pré-allocation massive d'arrays numpy (évite réallocations)
- Traitement par chunks avec libération mémoire explicite (gc.collect())
- Utilisation de dtypes optimisés (int8, object)
- Libération immédiate des données non utilisées

3. VALIDATION ET ROBUSTESSE
--------------------------
- Validation stricte des types et formats de données
- Gestion des erreurs avec fallback automatique
- Filtrage des données invalides (main_number = null)
- Diagnostic complet des données extraites

4. PERFORMANCE MAXIMALE
----------------------
- Numba JIT pour calculs intensifs (parallélisation automatique)
- msgspec pour structures de données optimisées
- ijson pour streaming sans charger tout en mémoire
- orjson pour parsing JSON ultra-rapide

================================================================================
XI. STRUCTURES MSGSPEC COMPLÈTES POUR VALIDATION DE SCHÉMA
================================================================================

1. DÉFINITION DES STRUCTURES OPTIMISÉES
--------------------------------------
```python
if MSGSPEC_AVAILABLE:
    from msgspec import Struct
    from typing import List, Dict, Any, Optional

    class CarteStruct(Struct):
        """Structure optimisée pour une carte de baccarat"""
        rang: str
        couleur: str
        valeur: int

    class MainStruct(Struct):
        """Structure optimisée pour une main de baccarat"""
        main_number: Optional[int]
        manche_pb_number: Optional[int]
        cartes_player: List[CarteStruct]
        cartes_banker: List[CarteStruct]
        total_cartes_distribuees: int
        score_player: int
        score_banker: int
        index1: Optional[int]  # SYNC/DESYNC
        cards_count: int
        index2: str  # A/B/C
        index3: str  # PLAYER/BANKER/TIE
        index5: str  # INDEX5 combiné
        timestamp: str

    class StatistiquesStruct(Struct):
        """Structure optimisée pour les statistiques d'une partie"""
        total_mains: int
        total_manches_pb: int
        total_ties: int
        cut_card_atteinte: bool
        cartes_restantes: int

    class PartieStruct(Struct):
        """Structure optimisée pour une partie complète"""
        partie_number: int
        burn_info: Dict[str, Any]
        statistiques: StatistiquesStruct
        mains: List[MainStruct]

    class DatasetStruct(Struct):
        """Structure optimisée pour le dataset complet"""
        metadata: Dict[str, Any]
        configuration: Dict[str, Any]
        parties: List[PartieStruct]

    class ChunkStruct(Struct):
        """Structure optimisée pour un chunk de parties"""
        parties: List[PartieStruct]
        chunk_id: int
        nb_parties: int
```

2. FONCTIONS DE PARSING MSGSPEC
------------------------------
```python
def parse_partie_msgspec(partie_data: dict) -> PartieStruct:
    """Parse une partie avec msgspec et validation"""
    try:
        # Conversion optimisée avec validation de schéma
        if isinstance(partie_data, dict):
            return msgspec.convert(partie_data, type=PartieStruct)
        else:
            return partie_data
    except Exception as e:
        # Fallback vers structure dict standard
        return partie_data

def parse_chunk_msgspec(chunk_data: list) -> list:
    """Parse un chunk de parties avec msgspec ultra-rapide"""
    try:
        parsed_parties = []
        for partie in chunk_data:
            parsed_partie = parse_partie_msgspec(partie)
            parsed_parties.append(parsed_partie)
        return parsed_parties
    except Exception as e:
        print(f"⚠️  Erreur parsing chunk msgspec : {e}")
        return chunk_data  # Fallback
```

================================================================================
XII. MÉTHODE AVANCÉE : PARSING PAR SEGMENTS POUR FICHIERS TRÈS VOLUMINEUX
================================================================================

1. LECTURE PAR SEGMENTS AVEC PARSING MANUEL
------------------------------------------
```python
def _charger_dataset_msgspec_chunks(self, file_handle) -> bool:
    """Chargement msgspec pur avec lecture par segments de taille optimale"""
    try:
        print("🚀 MSGSPEC CHUNKS : Lecture par segments optimisés")

        # Lire le fichier par segments pour éviter de charger tout en mémoire
        file_handle.seek(0)
        file_size = file_handle.seek(0, 2)  # Aller à la fin pour obtenir la taille
        file_handle.seek(0)  # Revenir au début

        print(f"📊 Taille fichier : {file_size / (1024*1024):.1f} MB")

        # Stratégie optimisée pour 28GB RAM : chunks massifs
        chunk_size_bytes = 500 * 1024 * 1024  # 500MB par chunk avec 28GB RAM
        parties_processed = 0

        # Lire le début pour trouver le début du tableau "parties"
        header_data = file_handle.read(1024).decode('utf-8')
        parties_start = header_data.find('"parties":[')

        if parties_start == -1:
            print("❌ Structure JSON non reconnue")
            return False

        # Repositionner après "parties":[
        file_handle.seek(parties_start + 11)

        buffer = ""
        bracket_count = 0
        current_partie = ""
        chunk_data = []

        while parties_processed < self.nb_parties_analyse:
            # Lire un segment
            segment = file_handle.read(chunk_size_bytes)
            if not segment:
                break

            buffer += segment.decode('utf-8', errors='ignore')

            # Parser les parties complètes dans le buffer
            i = 0
            while i < len(buffer) and parties_processed < self.nb_parties_analyse:
                char = buffer[i]
                current_partie += char

                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1

                    # Partie complète trouvée
                    if bracket_count == 0 and current_partie.strip():
                        try:
                            # Parser avec msgspec
                            if MSGSPEC_AVAILABLE:
                                partie_obj = msgspec.json.decode(current_partie.encode())
                            else:
                                partie_obj = json.loads(current_partie)

                            chunk_data.append(partie_obj)
                            parties_processed += 1

                            # Traiter chunk quand plein
                            if len(chunk_data) >= self.chunk_size:
                                self._traiter_chunk_streaming_msgspec(chunk_data)
                                chunk_data = []
                                gc.collect()

                        except Exception as e:
                            print(f"⚠️  Erreur parsing partie : {e}")

                        current_partie = ""

                i += 1

            # Garder la fin du buffer pour la prochaine itération
            last_brace = buffer.rfind('}')
            if last_brace > 0:
                buffer = buffer[last_brace + 1:]
            else:
                buffer = ""

        # Traiter le dernier chunk
        if chunk_data:
            self._traiter_chunk_streaming_msgspec(chunk_data)

        print(f"✅ MSGSPEC CHUNKS TERMINÉ : {parties_processed} parties traitées")
        return True

    except Exception as e:
        print(f"❌ Erreur msgspec chunks : {e}")
        return False
```

================================================================================
XIII. EXTRACTION ET CONVERSION DES DONNÉES OPTIMISÉES
================================================================================

1. EXTRACTION VERS ARRAYS PRÉ-ALLOUÉS
------------------------------------
```python
def extraire_sequences(self):
    """Extrait toutes les séquences temporelles avec optimisations révolutionnaires"""
    print("\n🔍 EXTRACTION OPTIMISÉE DES SÉQUENCES TEMPORELLES...")

    # Utiliser les arrays pré-alloués si disponibles
    if hasattr(self, 'sequences_preallocated') and any(self.sequences_counters.values()):
        print("🚀 Utilisation des arrays pré-alloués")
        # Redimensionner aux tailles réelles
        for key in self.sequences_preallocated.keys():
            actual_size = self.sequences_counters[key]
            if actual_size > 0:
                self.sequences[key] = self.sequences_preallocated[key][:actual_size].copy()
            else:
                # Dtype approprié selon le type de données
                if key in ['index2', 'index3', 'index5']:
                    self.sequences[key] = np.array([], dtype=object)  # Chaînes
                else:
                    self.sequences[key] = np.array([], dtype=np.int8)  # Entiers

        # Libération mémoire des arrays temporaires
        self.sequences_preallocated = None
        self.sequences_counters = None
        gc.collect()

        print(f"✅ Séquences extraites avec optimisations:")
        for key, seq in self.sequences.items():
            print(f"   {key}: {len(seq)} éléments")
        return

    # Fallback : extraction classique optimisée
    print("📂 Extraction classique avec optimisations")

    # Pré-allocation basée sur estimation - SUPPORT CHAÎNES
    estimated_size = self.nb_parties_analyse * 80
    self.sequences = {
        'index1': np.zeros(estimated_size, dtype=np.int8),
        'index2': np.empty(estimated_size, dtype=object),  # Object pour chaînes
        'index3': np.empty(estimated_size, dtype=object),  # Object pour chaînes
        'index5': np.empty(estimated_size, dtype=object),  # Object pour chaînes
        'cards_count': np.zeros(estimated_size, dtype=np.int8)
    }
    counters = {key: 0 for key in self.sequences.keys()}

    # Métadonnées temporelles
    self.sequences['partie_numbers'] = []
    self.sequences['main_numbers'] = []
    self.sequences['manche_numbers'] = []

    total_mains = 0
    mains_valides = 0

    for partie in self.data['parties']:
        partie_num = partie['partie_number']

        for main in partie['mains']:
            total_mains += 1

            # Ignorer les mains dummy
            if main.get('main_number') is None:
                continue

            mains_valides += 1

            # Extraction optimisée vers arrays pré-alloués avec validation
            for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
                if key in main and main[key] is not None and counters[key] < len(self.sequences[key]):
                    value = main[key]
                    try:
                        if key in ['index1', 'cards_count']:
                            # Entiers : validation et conversion STRICTE
                            if isinstance(value, str):
                                converted_value = int(value.strip())
                            else:
                                converted_value = int(value)

                            # Validation des valeurs INDEX1
                            if key == 'index1' and converted_value not in [0, 1]:
                                print(f"⚠️  INDEX1 valeur invalide: {converted_value} (attendu: 0 ou 1)")
                                continue

                            self.sequences[key][counters[key]] = converted_value
                            counters[key] += 1
                        else:
                            # Chaînes : validation et conversion STRICTE
                            converted_value = str(value).strip()
                            if converted_value:  # Ignorer les chaînes vides
                                # Validation des valeurs INDEX2/3/5
                                if key == 'index2' and converted_value not in ['A', 'B', 'C']:
                                    print(f"⚠️  INDEX2 valeur invalide: {converted_value}")
                                    continue
                                elif key == 'index3' and converted_value not in ['BANKER', 'PLAYER', 'TIE']:
                                    print(f"⚠️  INDEX3 valeur invalide: {converted_value}")
                                    continue
                                elif key == 'index5':
                                    # Validation format INDEX5
                                    expected_pattern = r'^[01]_[ABC]_(BANKER|PLAYER|TIE)$'
                                    if not re.match(expected_pattern, converted_value):
                                        print(f"⚠️  INDEX5 format invalide: {converted_value}")
                                        continue

                                self.sequences[key][counters[key]] = converted_value
                                counters[key] += 1
                    except (ValueError, TypeError) as e:
                        print(f"⚠️  Erreur conversion {key}: {value} -> {e}")

            # Métadonnées
            self.sequences['partie_numbers'].append(partie_num)
            self.sequences['main_numbers'].append(main['main_number'])
            self.sequences['manche_numbers'].append(main.get('manche_pb_number'))

    # Redimensionner aux tailles réelles et libération mémoire
    for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
        actual_size = counters[key]
        if actual_size > 0:
            self.sequences[key] = self.sequences[key][:actual_size].copy()
        else:
            # Dtype approprié selon le type de données
            if key in ['index2', 'index3', 'index5']:
                self.sequences[key] = np.array([], dtype=object)  # Chaînes
            else:
                self.sequences[key] = np.array([], dtype=np.int8)  # Entiers

    # Conversion métadonnées en numpy
    for key in ['partie_numbers', 'main_numbers', 'manche_numbers']:
        if key in self.sequences:
            self.sequences[key] = np.array(self.sequences[key])

    # Libération mémoire finale
    gc.collect()

    print(f"✅ Séquences extraites avec optimisations révolutionnaires:")
    print(f"   • Parties analysées : {len(self.data['parties']) if self.data else 0}")
    print(f"   • Total mains : {total_mains}")
    print(f"   • Mains valides : {mains_valides}")
    for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
        if key in self.sequences:
            seq = self.sequences[key]
            print(f"   • {key}: {len(seq)} éléments ({seq.nbytes / 1024:.1f} KB)")
    print(f"   • Puissance statistique : ÉLEVÉE (n > 10000)")

    # Validation critique des données extraites
    self._valider_donnees_extraites()

    return mains_valides
```

CONCLUSION : Cette architecture permet de traiter efficacement des fichiers JSON
de plusieurs GB en optimisant l'utilisation de la RAM disponible (28GB) et en
s'adaptant automatiquement à la taille du fichier et aux librairies disponibles.
