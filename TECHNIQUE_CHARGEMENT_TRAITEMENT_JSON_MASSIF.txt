TECHNIQUE DE CHARGEMENT ET TRAITEMENT OPTIMAL D'UN FICHIER JSON MASSIF
=======================================================================
Basé sur l'analyse de : analyseur_scientifique_revolutionnaire.py

OBJECTIF : Charger et traiter efficacement des fichiers JSON de plusieurs GB
CONTEXTE : Système optimisé pour 28GB RAM, fichiers jusqu'à 5GB+

================================================================================
I. ARCHITECTURE MULTI-NIVEAUX DE CHARGEMENT
================================================================================

1. DÉTECTION AUTOMATIQUE DE LA MÉTHODE OPTIMALE
-----------------------------------------------
```python
def charger_dataset(self) -> bool:
    file_size_mb = os.path.getsize(self.dataset_path) / (1024 * 1024)
    
    if file_size_mb > 5000:  # Fichiers > 5GB : streaming obligatoire
        if HAS_IJSON:
            return self._charger_dataset_streaming()
        else:
            return self._charger_dataset_standard()
    elif self.use_streaming and HAS_IJSON:
        return self._charger_dataset_streaming()
    elif MSGSPEC_AVAILABLE and file_size_mb < 500:  # msgspec pour < 500MB
        return self._charger_dataset_msgspec()
    else:
        return self._charger_dataset_standard()
```

2. HIÉRARCHIE DES MÉTHODES (PAR ORDRE DE PERFORMANCE)
----------------------------------------------------
- STREAMING HYBRIDE (ijson + msgspec) : Fichiers > 5GB
- MSGSPEC PUR : Fichiers < 500MB, performance maximale
- ORJSON STANDARD : Fallback ultra-rapide
- JSON STANDARD : Fallback de base

================================================================================
II. LIBRAIRIES D'OPTIMISATION CRITIQUES
================================================================================

1. LIBRAIRIES DE PARSING JSON HAUTE PERFORMANCE
----------------------------------------------
```python
# STREAMING JSON POUR DATASETS MASSIFS
try:
    import ijson
    HAS_IJSON = True
    print("🚀 ijson disponible - Streaming JSON activé")
except ImportError:
    HAS_IJSON = False

# JSON ULTRA-RAPIDE
try:
    import orjson
    HAS_ORJSON = True
    print("🚀 orjson disponible - Parsing JSON ultra-rapide")
except ImportError:
    HAS_ORJSON = False

# STRUCTURES OPTIMISÉES
try:
    import msgspec
    from msgspec import Struct
    MSGSPEC_AVAILABLE = True
    print("🚀 msgspec disponible - Performances révolutionnaires activées")
except ImportError:
    MSGSPEC_AVAILABLE = False
```

2. OPTIMISATIONS NUMÉRIQUES
---------------------------
```python
# OPTIMISATIONS NUMBA JIT
try:
    from numba import jit, prange
    HAS_NUMBA = True
    print("🚀 numba disponible - Calculs JIT ultra-rapides")
except ImportError:
    HAS_NUMBA = False

import gc  # Garbage collection explicite
```

================================================================================
III. MÉTHODE 1 : STREAMING HYBRIDE (FICHIERS > 5GB)
================================================================================

1. PRINCIPE : ijson STREAMING + msgspec PARSING
----------------------------------------------
```python
def _charger_dataset_streaming(self) -> bool:
    print(f"🌊 STREAMING HYBRIDE RÉVOLUTIONNAIRE : ijson + msgspec")
    print(f"📦 Traitement par chunks de {self.chunk_size} parties")
    
    parties_processed = 0
    chunk_data = []
    
    with open(self.dataset_path, 'rb') as f:
        # Utiliser ijson.items pour extraire directement les parties
        parties_stream = ijson.items(f, 'parties.item')
        
        for partie in parties_stream:
            chunk_data.append(partie)
            parties_processed += 1
            
            # Traiter chunk quand plein avec msgspec
            if len(chunk_data) >= self.chunk_size:
                self._traiter_chunk_streaming_msgspec(chunk_data)
                chunk_data = []
                gc.collect()  # Libération mémoire explicite
            
            # Arrêter si limite atteinte
            if parties_processed >= self.nb_parties_analyse:
                break
        
        # Traiter le dernier chunk
        if chunk_data:
            self._traiter_chunk_streaming_msgspec(chunk_data)
```

2. TRAITEMENT DES CHUNKS AVEC MSGSPEC
------------------------------------
```python
def _traiter_chunk_streaming_msgspec(self, chunk_data):
    # OPTIMISATION MSGSPEC : Parser le chunk entier si msgspec disponible
    if MSGSPEC_AVAILABLE:
        try:
            parsed_chunk = parse_chunk_msgspec(chunk_data)
            chunk_to_process = parsed_chunk
        except Exception as e:
            chunk_to_process = chunk_data  # Fallback
    else:
        chunk_to_process = chunk_data
    
    # Extraction optimisée des séquences vers arrays pré-alloués
    for partie_data in chunk_to_process:
        # Gestion des structures msgspec et dict
        if hasattr(partie_data, 'mains'):  # Structure msgspec
            mains = partie_data.mains
        elif 'mains' in partie_data:  # Structure dict
            mains = partie_data['mains']
        else:
            continue
        
        for main_data in mains:
            # FILTRAGE : Ignorer les mains null (main_number = null)
            main_number = None
            if hasattr(main_data, 'main_number'):
                main_number = getattr(main_data, 'main_number', None)
            else:
                main_number = main_data.get('main_number')
            
            if main_number is None:
                continue
            
            # Extraction vers arrays pré-alloués avec validation
            # [Code d'extraction optimisée...]
```

================================================================================
IV. MÉTHODE 2 : MSGSPEC PUR (FICHIERS < 500MB)
================================================================================

1. STRUCTURES MSGSPEC OPTIMISÉES
-------------------------------
```python
if MSGSPEC_AVAILABLE:
    class CarteStruct(Struct):
        rang: str
        couleur: str
        valeur: int
    
    class MainStruct(Struct):
        main_number: Optional[int]
        manche_pb_number: Optional[int]
        cartes_player: List[CarteStruct]
        cartes_banker: List[CarteStruct]
        total_cartes_distribuees: int
        score_player: int
        score_banker: int
        index1: Optional[int]
        cards_count: int
        index2: str
        index3: str
        index5: str
        timestamp: str
    
    class DatasetStruct(Struct):
        metadata: Dict[str, Any]
        configuration: Dict[str, Any]
        parties: List[PartieStruct]
```

2. CHARGEMENT MSGSPEC ULTRA-RAPIDE
---------------------------------
```python
def _charger_dataset_msgspec(self) -> bool:
    with open(self.dataset_path, 'rb') as f:
        data_raw = f.read()
        print(f"📊 Taille fichier : {len(data_raw) / (1024*1024):.1f} MB")
    
    # Parsing ultra-rapide avec validation de schéma
    try:
        self.data = msgspec.json.decode(data_raw, type=DatasetStruct)
        print("✅ Parsing msgspec réussi avec validation intégrée")
        
        # Conversion pour compatibilité avec le reste du code
        self.data = {
            'metadata': self.data.metadata,
            'configuration': self.data.configuration,
            'parties': [/* conversion structures */]
        }
    except Exception as e:
        return self._charger_dataset_standard()  # Fallback
```

================================================================================
V. MÉTHODE 3 : ORJSON STANDARD (FALLBACK RAPIDE)
================================================================================

```python
def _charger_dataset_standard(self) -> bool:
    # Utiliser orjson si disponible pour parsing ultra-rapide
    if HAS_ORJSON:
        with open(self.dataset_path, 'rb') as f:
            self.data = orjson.loads(f.read())
        print("🚀 Parsing orjson ultra-rapide utilisé")
    else:
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print("📂 Parsing JSON standard utilisé")
    
    # Traitement par chunks pour optimiser la mémoire
    if len(self.data['parties']) > self.nb_parties_analyse:
        parties_to_process = self.data['parties'][:self.nb_parties_analyse]
        
        # Libérer immédiatement les parties non utilisées
        self.data['parties'] = None
        gc.collect()
        
        # Traiter par chunks massifs avec 28GB RAM
        for i in range(0, len(parties_to_process), self.chunk_size):
            chunk = parties_to_process[i:i + self.chunk_size]
            self._traiter_chunk_standard(chunk)
            
            # Libération mémoire moins fréquente avec 28GB RAM
            if i % (self.chunk_size * 10) == 0:  # Tous les 10 chunks
                gc.collect()
```

================================================================================
VI. OPTIMISATIONS MÉMOIRE CRITIQUES
================================================================================

1. PRÉ-ALLOCATION MASSIVE POUR 28GB RAM
--------------------------------------
```python
# OPTIMISATIONS RÉVOLUTIONNAIRES POUR DATASETS MASSIFS - 28GB RAM DISPONIBLE
self.chunk_size = 5000  # Chunks massifs avec 28GB RAM
self.use_streaming = nb_parties_analyse > 10000  # Streaming agressif
self.memory_limit_mb = 20480  # Limite mémoire 20GB (réserve 8GB système)

# PRÉ-ALLOCATION MASSIVE POUR 28GB RAM - PERFORMANCE RÉVOLUTIONNAIRE
estimated_mains = nb_parties_analyse * 120  # Estimation généreuse
buffer_multiplier = 2.0  # Buffer 2x plus grand pour éviter réallocations
final_size = int(estimated_mains * buffer_multiplier)

self.sequences_preallocated = {
    'index1': np.zeros(final_size, dtype=np.int8),
    'index2': np.empty(final_size, dtype=object),  # Object pour chaînes
    'index3': np.empty(final_size, dtype=object),  # Object pour chaînes
    'index5': np.empty(final_size, dtype=object),  # Object pour chaînes
    'cards_count': np.zeros(final_size, dtype=np.int8)
}
self.sequences_counters = {key: 0 for key in self.sequences_preallocated.keys()}
```

2. GESTION EXPLICITE DE LA MÉMOIRE
---------------------------------
```python
import gc  # Garbage collection explicite

# Libération mémoire après chaque chunk
if len(chunk_data) >= self.chunk_size:
    self._traiter_chunk_streaming_msgspec(chunk_data)
    chunk_data = []
    gc.collect()  # Force garbage collection

# Libération mémoire moins fréquente avec 28GB RAM
if i % (self.chunk_size * 10) == 0:  # Tous les 10 chunks (50,000 parties)
    gc.collect()

# Libération des arrays temporaires après extraction
self.sequences_preallocated = None
self.sequences_counters = None
gc.collect()
```

================================================================================
VII. VALIDATION ET CONVERSION DE DONNÉES
================================================================================

1. VALIDATION STRICTE AVEC CONVERSION DE TYPE
--------------------------------------------
```python
def _traiter_chunk_standard(self, chunk_parties):
    for partie in chunk_parties:
        if 'mains' in partie:
            for main in partie['mains']:
                # FILTRAGE : Ignorer les mains null (main_number = null)
                if main.get('main_number') is None:
                    continue

                # Extraction optimisée vers arrays pré-alloués avec validation
                for index_name in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
                    if index_name in main and main[index_name] is not None:
                        counter = self.sequences_counters[index_name]
                        if counter < len(self.sequences_preallocated[index_name]):
                            value = main[index_name]

                            if index_name in ['index1', 'cards_count']:
                                # Entiers : validation et conversion STRICTE
                                try:
                                    if isinstance(value, str):
                                        converted_value = int(value.strip())
                                    else:
                                        converted_value = int(value)

                                    # Validation des valeurs INDEX1
                                    if index_name == 'index1' and converted_value not in [0, 1]:
                                        print(f"⚠️  INDEX1 valeur invalide: {converted_value}")
                                        continue

                                    self.sequences_preallocated[index_name][counter] = converted_value
                                    self.sequences_counters[index_name] += 1
                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Erreur conversion {index_name}: {value} -> {e}")
                            else:
                                # Chaînes : validation et conversion STRICTE
                                try:
                                    converted_value = str(value).strip()
                                    if converted_value:  # Ignorer les chaînes vides
                                        # Validation des valeurs INDEX2/3/5
                                        if index_name == 'index2' and converted_value not in ['A', 'B', 'C']:
                                            print(f"⚠️  INDEX2 valeur invalide: {converted_value}")
                                            continue
                                        elif index_name == 'index3' and converted_value not in ['BANKER', 'PLAYER', 'TIE']:
                                            print(f"⚠️  INDEX3 valeur invalide: {converted_value}")
                                            continue
                                        elif index_name == 'index5':
                                            # Validation format INDEX5
                                            expected_pattern = r'^[01]_[ABC]_(BANKER|PLAYER|TIE)$'
                                            if not re.match(expected_pattern, converted_value):
                                                print(f"⚠️  INDEX5 format invalide: {converted_value}")
                                                continue

                                        self.sequences_preallocated[index_name][counter] = converted_value
                                        self.sequences_counters[index_name] += 1
                                except (ValueError, TypeError) as e:
                                    print(f"⚠️  Erreur conversion {index_name}: {value} -> {e}")
```

2. VALIDATION CRITIQUE DES DONNÉES EXTRAITES
-------------------------------------------
```python
def _valider_donnees_extraites(self):
    print(f"\n🔍 VALIDATION CRITIQUE DES DONNÉES EXTRAITES :")

    for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
        if key in self.sequences:
            seq = self.sequences[key]
            print(f"   • {key}: {len(seq)} éléments")

            if len(seq) > 0:
                # Échantillon des premières valeurs
                sample = seq[:10] if len(seq) >= 10 else seq
                print(f"     → Échantillon : {sample}")

                # Valeurs uniques
                if key == 'index1':
                    unique_vals = np.unique(seq)
                    print(f"     → Valeurs uniques INDEX1 : {unique_vals}")
                    if len(unique_vals) == 2 and 0 in unique_vals and 1 in unique_vals:
                        print(f"     → ✅ INDEX1 valide (0/1)")
                    else:
                        print(f"     → ❌ INDEX1 invalide : {unique_vals}")

                elif key == 'index2':
                    unique_vals = np.unique(seq)
                    expected = {'A', 'B', 'C'}
                    if set(unique_vals).issubset(expected):
                        print(f"     → ✅ INDEX2 valide (A/B/C)")
                    else:
                        print(f"     → ❌ INDEX2 invalide : {unique_vals}")

                elif key == 'index3':
                    unique_vals = np.unique(seq)
                    expected = {'BANKER', 'PLAYER', 'TIE'}
                    if set(unique_vals).issubset(expected):
                        print(f"     → ✅ INDEX3 valide (BANKER/PLAYER/TIE)")
                    else:
                        print(f"     → ❌ INDEX3 invalide : {unique_vals}")
            else:
                print(f"     → ❌ SÉQUENCE VIDE !")
```

================================================================================
VIII. OPTIMISATIONS NUMBA JIT POUR CALCULS INTENSIFS
================================================================================

1. FONCTIONS JIT COMPILÉES POUR PERFORMANCE MAXIMALE
---------------------------------------------------
```python
if HAS_NUMBA:
    @jit(nopython=True, parallel=True, cache=True)
    def calcul_entropie_batch_jit(sequences_matrix):
        """Calcul d'entropies en batch ultra-rapide avec Numba JIT"""
        nb_sequences = sequences_matrix.shape[0]
        entropies = np.zeros(nb_sequences, dtype=np.float64)

        for i in prange(nb_sequences):
            sequence = sequences_matrix[i]
            # Calcul entropie Shannon optimisé
            counts = np.bincount(sequence.astype(np.int32))
            total = len(sequence)
            entropie = 0.0
            for count in counts:
                if count > 0:
                    p = count / total
                    entropie -= p * np.log2(p)
            entropies[i] = entropie

        return entropies

    @jit(nopython=True, parallel=True, cache=True)
    def conversion_sequences_batch_jit(sequences_list):
        """Conversion optimisée de listes en arrays numpy"""
        return np.array(sequences_list, dtype=np.int32)
```

2. FALLBACK SANS JIT
-------------------
```python
else:
    def calcul_entropie_batch_jit(sequences_matrix):
        """Version fallback sans JIT"""
        entropies = []
        for sequence in sequences_matrix:
            counts = np.bincount(sequence.astype(np.int32))
            total = len(sequence)
            entropie = 0.0
            for count in counts:
                if count > 0:
                    p = count / total
                    entropie -= p * np.log2(p)
            entropies.append(entropie)
        return np.array(entropies)
```

================================================================================
IX. CONFIGURATION SYSTÈME OPTIMISÉE
================================================================================

1. PARAMÈTRES POUR 28GB RAM
--------------------------
```python
# OPTIMISATIONS RÉVOLUTIONNAIRES POUR DATASETS MASSIFS - 28GB RAM DISPONIBLE
self.chunk_size = 5000  # Chunks massifs avec 28GB RAM
self.use_streaming = nb_parties_analyse > 10000  # Streaming agressif
self.memory_limit_mb = 20480  # Limite mémoire 20GB (réserve 8GB pour le système)

print(f"🚀 Mode streaming: {'Activé' if self.use_streaming else 'Désactivé'}")
print(f"📦 Taille chunks: {self.chunk_size:,} parties")
print(f"💾 Limite mémoire: {self.memory_limit_mb:,} MB ({self.memory_limit_mb/1024:.1f} GB)")
print(f"🎯 RAM DISPONIBLE : 28 GB - Configuration optimisée pour performance maximale")
```

2. SEUILS DE DÉCISION AUTOMATIQUE
--------------------------------
```python
if file_size_mb > 5000:  # Fichiers > 5GB : streaming obligatoire
    print("🌊 Fichier volumineux détecté - Mode streaming forcé")
    if HAS_IJSON:
        return self._charger_dataset_streaming()
    else:
        return self._charger_dataset_standard()
elif self.use_streaming and HAS_IJSON:
    return self._charger_dataset_streaming()
elif MSGSPEC_AVAILABLE and file_size_mb < 500:  # msgspec seulement pour fichiers < 500MB
    return self._charger_dataset_msgspec()
else:
    return self._charger_dataset_standard()
```

================================================================================
X. RÉSUMÉ DES TECHNIQUES CLÉS
================================================================================

1. HIÉRARCHIE DES PERFORMANCES
-----------------------------
1. STREAMING HYBRIDE (ijson + msgspec) : Fichiers > 5GB
2. MSGSPEC PUR : Fichiers < 500MB, validation de schéma intégrée
3. ORJSON : Parsing JSON 2-3x plus rapide que json standard
4. JSON STANDARD : Fallback de base

2. OPTIMISATIONS MÉMOIRE CRITIQUES
---------------------------------
- Pré-allocation massive d'arrays numpy (évite réallocations)
- Traitement par chunks avec libération mémoire explicite (gc.collect())
- Utilisation de dtypes optimisés (int8, object)
- Libération immédiate des données non utilisées

3. VALIDATION ET ROBUSTESSE
--------------------------
- Validation stricte des types et formats de données
- Gestion des erreurs avec fallback automatique
- Filtrage des données invalides (main_number = null)
- Diagnostic complet des données extraites

4. PERFORMANCE MAXIMALE
----------------------
- Numba JIT pour calculs intensifs (parallélisation automatique)
- msgspec pour structures de données optimisées
- ijson pour streaming sans charger tout en mémoire
- orjson pour parsing JSON ultra-rapide

CONCLUSION : Cette architecture permet de traiter efficacement des fichiers JSON
de plusieurs GB en optimisant l'utilisation de la RAM disponible (28GB) et en
s'adaptant automatiquement à la taille du fichier et aux librairies disponibles.
