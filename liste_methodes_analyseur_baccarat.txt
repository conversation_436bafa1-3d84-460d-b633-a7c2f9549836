LISTE COMPLÈTE DES MÉTHODES - ANALYSEUR BACCARAT LUPASCO
========================================================

🔬 EntropySequencePredictor : Approche entropique (Shannon, ApEn/SampEn)
------------------------------------------------------------------------
1. __init__(self, m=2, r_factor=0.2)
2. encode_sequences(self, sequences)
3. calculate_approximate_entropy(self)
4. calculate_sample_entropy(self)
5. build_pattern_database(self, sequences)
6. predict_third_most_probable(self, v1, v2)

🌀 FractalBaccaratPredictor : Approche fractale (Carnegie Mellon F4)
-------------------------------------------------------------------
7. __init__(self, max_lag=10)
8. encode_sequences(self, sequences)
9. compute_fdl_plot(self)
10. build_embedding_space(self, sequences)
11. predict_fractal(self, v1, v2)

🚀 AnalyseurBaccaratLupascoRevolutionnaire : Orchestrateur principal unifiant les 3 approches
---------------------------------------------------------------------------------------------
12. __init__(self, dataset_path: str, chunk_size: int = 5000)
13. charger_dataset(self) -> bool
14. _charger_streaming(self) -> bool
15. _charger_standard(self) -> bool
16. _traiter_chunk(self, chunk_parties: List[Dict]) -> None
17. _valider_index5(self, index5: str) -> bool
18. extraire_sequences_ordre3(self) -> int
19. calculer_probabilites_conditionnelles(self) -> None
20. _calculer_information_mutuelle(self)
21. predire_v3_optimal(self, v1: str, v2: str) -> Tuple[str, float]
22. _predire_approche_unifiee(self, v1: str, v2: str) -> Tuple[str, float]
23. _appliquer_regles_lupasco(self, v1: str, v2: str) -> Dict[str, float]
24. _predire_fallback(self) -> Tuple[str, float]
25. valider_predictions(self, test_ratio: float = 0.2) -> Dict[str, float]
26. _calculer_significativite(self) -> float
27. generer_rapport_complet(self) -> str
28. sauvegarder_resultats(self, output_dir: str = "resultats") -> None

FONCTIONS UTILITAIRES GLOBALES
------------------------------
29. calculate_chebyshev_distance(x, y)
30. calculate_entropy_fast(probabilities)
31. main()

MÉTHODES INTERNES (NESTED FUNCTIONS)
------------------------------------
32. _phi(m) [dans calculate_approximate_entropy]
33. _count_matches(m, exclude_self=False) [dans calculate_sample_entropy]

TOTAL : 33 méthodes principales + 2 fonctions internes = 35 méthodes
