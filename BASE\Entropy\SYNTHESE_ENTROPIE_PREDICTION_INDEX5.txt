SYNTHÈSE COMPLÈTE - ENTROPIE ET PRÉDICTION DE SÉQUENCES INDEX5
================================================================

OBJECTIF PRINCIPAL:
Trouver la troisième valeur la plus probable d'une séquence de longueur 3 d'index5 
connaissant les deux premières valeurs de cette séquence, dans un dataset connu.

I. FONDEMENTS THÉORIQUES ESSENTIELS
===================================

1. THÉORIE DE L'INFORMATION (Shannon)
-------------------------------------
- Entropie de Shannon: H(X) = -∑ p(x) log p(x)
- Mesure la quantité d'information moyenne dans un système
- Plus l'entropie est élevée, plus le système est imprévisible
- Entropie conditionnelle: H(Y|X) = -∑∑ p(x,y) log p(y|x)

2. ENTROPIE APPROXIMATIVE (ApEn)
--------------------------------
Formule principale:
ApEn(m,r,N) = φ^m(r) - φ^(m+1)(r)

où φ^m(r) = (N-m+1)^(-1) ∑[i=1 to N-m+1] ln C_i^m(r)

C_i^m(r) = (N-m+1)^(-1) × [nombre de vecteurs x_m(j) à distance ≤ r de x_m(i)]

Paramètres critiques:
- m: dimension d'embedding (généralement m=2)
- r: filtre de bruit (recommandé: 0.1σ à 0.25σ, où σ = écart-type)
- N: longueur de la série

3. ENTROPIE D'ÉCHANTILLON (SampEn) - PLUS PRÉCISE
-------------------------------------------------
Formule principale:
SampEn(m,r,N) = -log[A^m(r)/B^m(r)]

B^m(r) = probabilité que deux séquences soient similaires pour m points
A^m(r) = probabilité que deux séquences soient similaires pour m+1 points

Avantages sur ApEn:
- Pas d'auto-comptage (j ≠ i)
- Comportement monotone décroissant avec r
- Plus stable et consistant

4. PROBABILITÉS CONDITIONNELLES POUR PRÉDICTION
-----------------------------------------------
P(X₃|X₁,X₂) = P(X₁,X₂,X₃) / P(X₁,X₂)

Pour séquences d'index5:
- Calculer fréquences des triplets (v₁,v₂,v₃)
- Calculer fréquences des doublets (v₁,v₂)
- Probabilité conditionnelle = freq(v₁,v₂,v₃) / freq(v₁,v₂)

II. ALGORITHMES IMPLÉMENTÉS (R/Python)
======================================

1. ALGORITHME ApEn ADAPTÉ POUR PRÉDICTION
-----------------------------------------
```python
def calculate_conditional_probabilities(sequence, m=2):
    """
    Calcule les probabilités conditionnelles pour prédiction
    """
    N = len(sequence)
    patterns = {}
    
    # Compter tous les motifs de longueur m et m+1
    for i in range(N - m):
        pattern_m = tuple(sequence[i:i+m])
        pattern_m1 = tuple(sequence[i:i+m+1])
        
        if pattern_m not in patterns:
            patterns[pattern_m] = {'count': 0, 'next_values': {}}
        
        patterns[pattern_m]['count'] += 1
        
        if i < N - m:  # Si on peut avoir le m+1ème élément
            next_val = sequence[i+m]
            if next_val not in patterns[pattern_m]['next_values']:
                patterns[pattern_m]['next_values'][next_val] = 0
            patterns[pattern_m]['next_values'][next_val] += 1
    
    return patterns

def predict_third_value(v1, v2, patterns):
    """
    Prédit la troisième valeur la plus probable
    """
    pattern = (v1, v2)
    if pattern not in patterns:
        return None
    
    next_values = patterns[pattern]['next_values']
    if not next_values:
        return None
    
    # Calculer probabilités
    total = sum(next_values.values())
    probabilities = {val: count/total for val, count in next_values.items()}
    
    # Trier par probabilité décroissante
    sorted_probs = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
    
    return sorted_probs
```

2. ALGORITHME SampEn POUR VALIDATION
------------------------------------
```python
def sample_entropy(sequence, m=2, r=0.2):
    """
    Calcule SampEn pour valider la complexité de la séquence
    """
    N = len(sequence)
    
    def _maxdist(xi, xj, m):
        return max([abs(ua - va) for ua, va in zip(xi, xj)])
    
    def _phi(m):
        patterns = np.array([sequence[i:i+m] for i in range(N - m + 1)])
        C = np.zeros(N - m + 1)
        
        for i in range(N - m + 1):
            template = patterns[i]
            distances = [_maxdist(template, patterns[j], m) 
                        for j in range(N - m + 1) if i != j]
            C[i] = sum(d <= r * np.std(sequence) for d in distances)
        
        return C
    
    A = _phi(m + 1)
    B = _phi(m)
    
    return -np.log(A.sum() / B.sum())
```

III. MÉTRIQUES DE DISTANCE ET SIMILARITÉ
========================================

1. DISTANCE DE CHEBYSHEV (utilisée dans ApEn/SampEn)
----------------------------------------------------
d[x(i), x(j)] = max|u(i+k) - u(j+k)| pour k = 0,1,...,m-1

2. DISTANCE EUCLIDIENNE (alternative)
------------------------------------
d[x(i), x(j)] = √[∑(u(i+k) - u(j+k))²]

3. SEUIL DE TOLÉRANCE r
----------------------
- r = 0.2 × σ (recommandation standard)
- σ = écart-type de la série
- Ajuster selon la précision souhaitée

IV. STRATÉGIE DE PRÉDICTION OPTIMALE
====================================

1. PRÉPARATION DES DONNÉES
-------------------------
- Normaliser la série: (u - mean(u)) / sd(u)
- Vérifier la stationnarité
- Éliminer les tendances si nécessaire

2. ANALYSE DES MOTIFS
--------------------
- Identifier tous les doublets (v₁,v₂) dans le dataset
- Pour chaque doublet, compter les valeurs v₃ qui suivent
- Calculer les fréquences relatives

3. CALCUL DES PROBABILITÉS
-------------------------
Pour un doublet donné (a,b):
P(v₃=c|v₁=a,v₂=b) = Count(a,b,c) / Count(a,b)

4. SÉLECTION DE LA PRÉDICTION
----------------------------
- Trier toutes les valeurs possibles par probabilité décroissante
- Sélectionner la 3ème valeur la plus probable
- Vérifier la significativité statistique

V. VALIDATION ET MÉTRIQUES
==========================

1. ENTROPIE CONDITIONNELLE
--------------------------
H(V₃|V₁,V₂) = -∑∑∑ P(v₁,v₂,v₃) log P(v₃|v₁,v₂)

2. INFORMATION MUTUELLE
----------------------
I(V₃;V₁,V₂) = H(V₃) - H(V₃|V₁,V₂)

3. PRÉCISION DE PRÉDICTION
-------------------------
Accuracy = Nombre de prédictions correctes / Nombre total de prédictions

VI. CONSIDÉRATIONS SPÉCIALES POUR INDEX5
========================================

1. NATURE DES VALEURS INDEX5
----------------------------
- Valeurs discrètes (probablement entières)
- Alphabet fini et connu
- Possibles contraintes de domaine

2. DÉPENDANCES TEMPORELLES
-------------------------
- Analyser l'autocorrélation
- Détecter les cycles ou périodicités
- Considérer les dépendances à long terme

3. OPTIMISATION DES PARAMÈTRES
-----------------------------
- Tester différentes valeurs de m (1, 2, 3)
- Ajuster r selon la distribution des valeurs
- Valider par validation croisée

VII. IMPLÉMENTATION PRATIQUE
============================

1. STRUCTURE DE DONNÉES RECOMMANDÉE
----------------------------------
```python
class SequencePredictor:
    def __init__(self, sequence):
        self.sequence = sequence
        self.patterns = {}
        self.entropy_measures = {}
    
    def analyze_patterns(self, m=2):
        # Analyse des motifs
        pass
    
    def predict_next(self, v1, v2, rank=3):
        # Prédiction du rang spécifié
        pass
    
    def validate_prediction(self):
        # Validation croisée
        pass
```

2. PIPELINE DE TRAITEMENT
------------------------
1. Chargement et nettoyage des données
2. Analyse exploratoire (distribution, stationnarité)
3. Extraction des motifs et calcul des probabilités
4. Prédiction avec ranking
5. Validation et métriques de performance

VIII. FORMULES MATHÉMATIQUES COMPLÈTES
=====================================

1. ENTROPIE DE RÉNYI (ordre 2) - Base de SampEn
-----------------------------------------------
H₂(X) = -log₂(∑ᵢ pᵢ²)

Propriété: H₁(X) > H₂(X) (SampEn est une borne inférieure)

2. INTÉGRALE DE CORRÉLATION (Grassberger-Procaccia)
--------------------------------------------------
C^m(r) = lim[N→∞] (1/N²) ∑∑ Θ(r - ||xᵢ - xⱼ||)

où Θ est la fonction de Heaviside

3. DIMENSION DE CORRÉLATION
--------------------------
D₂ = lim[r→0] log C(r) / log r

4. ENTROPIE DE KOLMOGOROV-SINAI
------------------------------
K = lim[m→∞] lim[r→0] lim[N→∞] [log C^m(r) - log C^(m+1)(r)]

IX. ALGORITHMES COMPLETS EN PYTHON
==================================

```python
import numpy as np
from collections import defaultdict, Counter
import pandas as pd

class EntropySequencePredictor:
    def __init__(self, sequence, normalize=True):
        self.original_sequence = sequence
        if normalize:
            self.sequence = self._normalize(sequence)
        else:
            self.sequence = sequence
        self.patterns = {}
        self.transition_matrix = {}

    def _normalize(self, seq):
        """Normalisation z-score"""
        seq = np.array(seq)
        return (seq - np.mean(seq)) / np.std(seq)

    def calculate_approximate_entropy(self, m=2, r=0.2):
        """Calcul ApEn complet selon Pincus"""
        N = len(self.sequence)
        sigma = np.std(self.sequence)
        r_threshold = r * sigma

        def _phi(m):
            patterns = []
            for i in range(N - m + 1):
                pattern = self.sequence[i:i+m]
                patterns.append(pattern)

            phi_sum = 0
            for i, template in enumerate(patterns):
                matches = 0
                for j, candidate in enumerate(patterns):
                    if self._max_distance(template, candidate) <= r_threshold:
                        matches += 1

                if matches > 0:
                    phi_sum += np.log(matches / len(patterns))

            return phi_sum / len(patterns)

        return _phi(m) - _phi(m + 1)

    def calculate_sample_entropy(self, m=2, r=0.2):
        """Calcul SampEn selon Richman-Moorman"""
        N = len(self.sequence)
        sigma = np.std(self.sequence)
        r_threshold = r * sigma

        def _count_matches(m, exclude_self=True):
            patterns = []
            for i in range(N - m + 1):
                patterns.append(self.sequence[i:i+m])

            total_matches = 0
            for i, template in enumerate(patterns):
                for j, candidate in enumerate(patterns):
                    if exclude_self and i == j:
                        continue
                    if self._max_distance(template, candidate) <= r_threshold:
                        total_matches += 1

            return total_matches

        A = _count_matches(m + 1, exclude_self=True)
        B = _count_matches(m, exclude_self=True)

        if A == 0 or B == 0:
            return float('inf')

        return -np.log(A / B)

    def _max_distance(self, x, y):
        """Distance de Chebyshev (max)"""
        return max(abs(a - b) for a, b in zip(x, y))

    def build_pattern_database(self, m=2):
        """Construction base de données des motifs"""
        N = len(self.sequence)
        patterns = defaultdict(lambda: {'count': 0, 'next_values': Counter()})

        for i in range(N - m):
            # Motif de longueur m
            pattern = tuple(self.sequence[i:i+m])
            patterns[pattern]['count'] += 1

            # Valeur suivante
            if i + m < N:
                next_val = self.sequence[i + m]
                patterns[pattern]['next_values'][next_val] += 1

        self.patterns = dict(patterns)
        return self.patterns

    def predict_third_most_probable(self, v1, v2):
        """Prédiction de la 3ème valeur la plus probable"""
        pattern = (v1, v2)

        if pattern not in self.patterns:
            return None, 0.0

        next_values = self.patterns[pattern]['next_values']
        if len(next_values) < 3:
            return None, 0.0

        # Calcul des probabilités
        total = sum(next_values.values())
        probabilities = [(val, count/total) for val, count in next_values.items()]

        # Tri par probabilité décroissante
        probabilities.sort(key=lambda x: x[1], reverse=True)

        if len(probabilities) >= 3:
            return probabilities[2]  # 3ème plus probable
        else:
            return None, 0.0

    def calculate_conditional_entropy(self, v1, v2):
        """Entropie conditionnelle H(V3|V1=v1,V2=v2)"""
        pattern = (v1, v2)

        if pattern not in self.patterns:
            return 0.0

        next_values = self.patterns[pattern]['next_values']
        total = sum(next_values.values())

        entropy = 0.0
        for count in next_values.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)

        return entropy

    def validate_predictions(self, test_size=0.2):
        """Validation croisée des prédictions"""
        N = len(self.sequence)
        split_point = int(N * (1 - test_size))

        # Division train/test
        train_seq = self.sequence[:split_point]
        test_seq = self.sequence[split_point:]

        # Construction modèle sur train
        temp_predictor = EntropySequencePredictor(train_seq, normalize=False)
        temp_predictor.build_pattern_database()

        # Test sur séquence test
        correct_predictions = 0
        total_predictions = 0

        for i in range(len(test_seq) - 2):
            v1, v2 = test_seq[i], test_seq[i+1]
            actual_v3 = test_seq[i+2]

            predicted_v3, prob = temp_predictor.predict_third_most_probable(v1, v2)

            if predicted_v3 is not None:
                total_predictions += 1
                if abs(predicted_v3 - actual_v3) < 1e-6:  # Tolérance numérique
                    correct_predictions += 1

        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        return accuracy, correct_predictions, total_predictions

# Exemple d'utilisation
def example_usage():
    # Séquence d'exemple (remplacer par vraies données index5)
    sequence = [1, 2, 1, 3, 2, 1, 2, 3, 1, 2, 1, 3, 2, 3, 1]

    predictor = EntropySequencePredictor(sequence, normalize=False)

    # Analyse entropique
    apen = predictor.calculate_approximate_entropy()
    sampen = predictor.calculate_sample_entropy()

    print(f"ApEn: {apen:.4f}")
    print(f"SampEn: {sampen:.4f}")

    # Construction base de motifs
    patterns = predictor.build_pattern_database()

    # Prédiction
    v1, v2 = 1, 2
    predicted_v3, probability = predictor.predict_third_most_probable(v1, v2)

    print(f"Pour la séquence ({v1}, {v2}, ?)")
    print(f"3ème valeur la plus probable: {predicted_v3} (prob: {probability:.4f})")

    # Validation
    accuracy, correct, total = predictor.validate_predictions()
    print(f"Précision: {accuracy:.2%} ({correct}/{total})")

if __name__ == "__main__":
    example_usage()
```

X. OPTIMISATIONS AVANCÉES
=========================

1. SÉLECTION AUTOMATIQUE DES PARAMÈTRES
---------------------------------------
- Grid search sur (m, r)
- Validation croisée k-fold
- Critère d'information (AIC, BIC)

2. TRAITEMENT DES SÉQUENCES LONGUES
----------------------------------
- Fenêtrage glissant
- Pondération temporelle
- Oubli exponentiel

3. GESTION DES VALEURS RARES
----------------------------
- Lissage de Laplace
- Interpolation
- Clustering des valeurs similaires

Cette synthèse complète contient tous les éléments théoriques et pratiques nécessaires
pour implémenter un système de prédiction de la troisième valeur la plus probable
dans une séquence d'index5, basé sur les principes d'entropie et de théorie de l'information.
